import axios from 'axios';
import {useEffect, useState} from 'react';
import {IUser} from '@/components/MCP/UserAvatarList';

const UserBaseInfoCache = new Map<string, IUser>();
const pendingRequests = new Map<string, Promise<IUser>>();

const getUserinfo = ({username}: {username: string}) => {
    // 如果已有相同username的请求正在进行，返回该Promise
    if (pendingRequests.has(username)) {
        return pendingRequests.get(username);
    }

    const request = axios.get(`/api/icafe/rest/v5/user?names=${username}`).then(
        (data: any) => {
            if (data?.data?.code === 200) {
                UserBaseInfoCache.set(username, data?.data?.data?.[0] ?? null);
                return data?.data?.data?.[0] ?? null;
            } else {
                throw new Error(data);
            }
        }
    ).finally(() => {
        // 请求完成后从pendingRequests中移除
        pendingRequests.delete(username);
    });

    pendingRequests.set(username, request);
    return request;
};

export const useUserBaseInfo = (username: string) => {
    const [info, setInfo] = useState<IUser>(null);
    useEffect(
        () => {
            if (username) {
                const cache = UserBaseInfoCache.get(username);
                if (cache) {
                    setInfo(cache);
                } else {
                    getUserinfo({username}).then(res => {
                        setInfo(res);
                    });
                }
            }
        },
        [setInfo, username]
    );
    return info;
};

export const usePrefetchUserInfos = (users: string[]) => {
    useEffect(
        () => {
            Promise.all(users.map(username => getUserinfo({username})));
        },
        [users]
    );
};
