/* eslint-disable max-lines */
import {use<PERSON><PERSON>back, MouseEvent, useMemo, useRef, useEffect, useState, memo} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button} from '@panda-design/components';
import {Flex, Divider, Typography, Tooltip} from 'antd';
import type {TooltipPlacement} from 'antd/es/tooltip';
import styled from '@emotion/styled';
import {css} from '@emotion/css';
import {MCPDetailLink, MCPPlaygroundLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {apiPostViewCount} from '@/api/mcp';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MCPCard from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import SvgEye from '@/icons/mcp/Eye';
import SvgCallCount from '@/icons/mcp/CallCount';
import SvgVip from '@/icons/mcp/Vip';
import {IconArrowRight1} from '@/icons/mcp';
import {colors} from '@/constants/colors';
import bg from '@/assets/mcp/cardBg.png';
import vipbg from '@/assets/mcp/cardVipBg.png';

const cardWrapperCss = css`
    position: relative;
    height: 213px;
`;

const containerCss = ({official}: {official?: boolean}) => css`
    padding: 17px 24px 12px;
    position: relative;
    height: 213px;
    border-radius: 6px;
    z-index: 1;
    background-color: #fff;
    transition: all 0.3s ease;
    &:hover {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background-color: #fff;
        background-image: url(${official ? vipbg : bg});
        background-repeat: no-repeat;
        background-size: contain;
        height: 264px;
        .hover-actions {
            opacity: 1;
            min-height: 52px;
            padding: 4px 24px 16px;
        }
    }
`;

const hoverActionsStyle = css`
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.3s ease;
`;

const DescriptionContainer = styled.div`
    margin: 17px 0 12px;
    font-size: 14px;
    line-height: 22px;
    position: relative;
    min-height: 44px;
    max-height: 44px;
    color: #545454;
    overflow: hidden;
`;

const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
    overflow: hidden;
`;

const EllipsisOverlay = styled.div`
    position: absolute;
    bottom: 9px;
    right: 12px;
    padding-left: 10px;
    pointer-events: none;
`;

const cardContentStyle = {
    overflow: 'hidden',
    flex: 1,
};

const protocolTextStyle = {
    color: '#8F8F8F',
    fontSize: 12,
    lineHeight: '18px',
};

const dividerStyle = {
    margin: '16px 0 8px',
};

const statsContainerStyle = css`
    color: ${colors['gray-7']};
    font-size: 12px;
    line-height: 20px;
    transition: color 0.2s ease;
    cursor: pointer;
    &:hover {
        color: ${colors.primary};
    }
`;

const iconStyle = {
    width: 14,
    height: 14,
    color: '#545454',
};

const actionButtonStyle = {
    fontSize: '12px',
    lineHeight: '20px',
    padding: 0,
    height: 20,
    gap: 4,
    color: '#545454',
};

const tagGroupOverrideStyle = css`
    height: 20px !important;

    .mcp-server-card-tag {
        height: 20px !important;
        line-height: 18px !important;
        padding: 1px 8px !important;

        &:hover {
            height: 20px !important;
            line-height: 18px !important;
        }
    }
`;

const fullWidthButtonStyle = {
    background: 'var(--Tokens-, #F2F2F2)',
    border: 'none',
    width: '100%',
    height: '32px',
    gap: 4,
};

const formatCount = (count: number): string => {
    if (count >= 10000) {
        return `${Math.floor(count / 10000)}w+`;
    }
    if (count >= 1000) {
        return `${Math.floor(count / 1000)}k+`;
    }
    return count.toString();
};

const formatProtocolType = (protocolType: string): string => {
    if (protocolType === 'Streamable_HTTP') {
        return 'Streamable HTTP';
    }
    return protocolType;
};

interface Props {
    server: MCPServerBase;
    refresh: () => void;
    onFavoriteChange?: (serverId: number, favorite: boolean) => void;
}
const SquireMCPCard = ({server, refresh, onFavoriteChange}: Props) => {
    const navigate = useNavigate();
    const descriptionRef = useRef<HTMLDivElement>(null);
    const cardRef = useRef<HTMLDivElement>(null);
    const [isDescriptionOverflowing, setIsDescriptionOverflowing] = useState(false);
    const [tooltipPlacement, setTooltipPlacement] = useState<TooltipPlacement>('top');

    useEffect(
        () => {
            const checkOverflow = () => {
                if (descriptionRef.current) {
                    const element = descriptionRef.current;
                    const textElement = element.querySelector('[data-description-text]');
                    if (textElement) {
                        const lineHeight = parseInt(getComputedStyle(textElement).lineHeight, 10);
                        const maxHeight = lineHeight * 2;
                        const isOverflowing = textElement.scrollHeight > maxHeight;
                        setIsDescriptionOverflowing(isOverflowing);
                    }
                }
            };

            const updateTooltipPlacement = () => {
                if (cardRef.current) {
                    const rect = cardRef.current.getBoundingClientRect();
                    const viewportHeight = window.innerHeight;
                    const cardCenter = rect.top + rect.height / 2;
                    const isInUpperHalf = cardCenter < viewportHeight / 2;
                    setTooltipPlacement(isInUpperHalf ? 'bottom' : 'top');
                }
            };

            const handleUpdate = () => {
                checkOverflow();
                updateTooltipPlacement();
            };

            setTimeout(handleUpdate, 0);
            window.addEventListener('resize', handleUpdate);
            window.addEventListener('scroll', updateTooltipPlacement);
            return () => {
                window.removeEventListener('resize', handleUpdate);
                window.removeEventListener('scroll', updateTooltipPlacement);
            };
        },
        [server.description]
    );

    const handleClick = useCallback(
        async () => {
            await apiPostViewCount({mcpServerId: server.id});
            navigate(MCPDetailLink.toUrl({mcpId: server.id}));
        },
        [navigate, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
        },
        [navigate, server.id]
    );

    const handlePlaygroundClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            window.open(MCPPlaygroundLink.toUrl({serverId: server.id}), '_blank');
        },
        [server.id]
    );

    const handleCallCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            const url = MCPDetailLink.toUrl({mcpId: server.id, tab: 'tools'});
            navigate(url);
        },
        [navigate, server]
    );

    const tags = useMemo(
        () => (server.labels ?? []).map((label, index) => ({
            id: label.id || index,
            label: label.labelValue,
        })),
        [server.labels]
    );

    return (
        <div ref={cardRef} className={cardWrapperCss} data-server-id={server.id}>
            <MCPCard
                vertical
                onClick={handleClick}
                className={containerCss({official: server.official})}
                official={server.official}
            >
                <Flex gap={16} align="center">
                    <div style={{position: 'relative', display: 'inline-block'}}>
                        <MCPServerAvatar
                            icon={server.icon}
                            style={server.official ? {
                                border: '2px solid',
                                borderImageSource:
                                    'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',
                                borderImageSlice: 1,
                            } : undefined}
                        />
                        {server.official && (
                            <SvgVip
                                style={{
                                    position: 'absolute',
                                    bottom: -7,
                                    right: -4,
                                    fontSize: '23px',
                                }}
                            />
                        )}
                    </div>
                    <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                        <Typography.Title level={4} ellipsis style={{color: '#181818'}}>
                            {server.name}
                        </Typography.Title>
                        <Flex align="center" gap={4}>
                            <Typography.Text style={protocolTextStyle}>
                                {getServerTypeText(server.serverProtocolType)}
                            </Typography.Text>
                            <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                            <Typography.Text style={protocolTextStyle}>
                                {formatProtocolType(server.serverProtocolType)}
                            </Typography.Text>
                        </Flex>
                    </Flex>
                </Flex>
                {server.description && isDescriptionOverflowing ? (
                    <Tooltip title={server.description} placement={tooltipPlacement}>
                        <DescriptionContainer ref={descriptionRef}>
                            <DescriptionText data-description-text>{server.description}</DescriptionText>
                            <EllipsisOverlay />
                        </DescriptionContainer>
                    </Tooltip>
                ) : (
                    <DescriptionContainer ref={descriptionRef}>
                        <DescriptionText data-description-text>{server.description || '暂无描述'}</DescriptionText>
                        <EllipsisOverlay />
                    </DescriptionContainer>
                )}
                <div className={tagGroupOverrideStyle}>
                    <TagGroup
                        labels={tags}
                        color="light-purple"
                        prefix={null}
                        style={{flexShrink: 1, overflow: 'hidden'}}
                        gap={4}
                    />
                </div>
                <Divider style={dividerStyle} />
                <Flex justify="space-between" align="center">
                    <Flex align="center" gap={12}>
                        <Tooltip title="浏览量">
                            <Flex
                                align="center"
                                gap={4}
                                onClick={handleViewCountClick}
                                className={statsContainerStyle}
                            >
                                <SvgEye style={iconStyle} />
                                {formatCount(server.serverMetrics?.viewCount || 0)}
                            </Flex>
                        </Tooltip>
                        <Tooltip title="调用量">
                            <Flex
                                align="center"
                                gap={4}
                                className={statsContainerStyle}
                                onClick={handleCallCountClick}
                            >
                                <SvgCallCount style={iconStyle} />
                                {formatCount(server.serverMetrics?.callCount || 0)}
                            </Flex>
                        </Tooltip>
                    </Flex>
                    <Flex align="center">
                        <MCPCollectButton
                            refresh={refresh}
                            favorite={server.favorite}
                            serverId={server.id}
                            showText
                            style={actionButtonStyle}
                            onFavoriteChange={onFavoriteChange}
                        />
                        <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                        <MCPSubscribeButton
                            refresh={refresh}
                            workspaceId={server.workspaceId}
                            id={server.id}
                            showText
                            style={actionButtonStyle}
                        />
                    </Flex>
                </Flex>
                <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                    <Button onClick={handlePlaygroundClick} style={fullWidthButtonStyle}>
                        <IconArrowRight1 style={{fontSize: 16}} />去MCP Playground使用
                    </Button>
                </Flex>
            </MCPCard>
        </div>
    );
};

export default memo(SquireMCPCard);
