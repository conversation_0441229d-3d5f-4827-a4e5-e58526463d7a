
export const enum ServerProtocol {
    /**
     * 全部
     */
    ALL = 'all',
    /**
     * SSE
     */
    SSE = 'sse',
    /**
     * STDIO
     */
    STDIO = 'stdio',
    /**
     * Streamable HTTP
     */
    STREAMABLE_HTTP = 'streamable_http',
}

const ServerProtocolLabel = {
    [ServerProtocol.ALL]: '全部',
    [ServerProtocol.SSE]: 'SSE',
    [ServerProtocol.STDIO]: 'STDIO',
    [ServerProtocol.STREAMABLE_HTTP]: 'Streamable HTTP',
};

export const serverProtocolFilterOptions = [
    ServerProtocol.ALL,
    ServerProtocol.SSE,
    ServerProtocol.STDIO,
    ServerProtocol.STREAMABLE_HTTP,
].map(key => ({
    label: ServerProtocolLabel[key],
    value: key,
}));


export const enum ServerType {
    /**
     * 全部
     */
    ALL = 'all',
    /**
     * 标准MCP
     */
    STANDARD = 'external',
    /**
     * Remote
     */
    REMOTE = 'openapi',
    /**
     * Local
     */
    LOCAL = 'script',
}

const ServerTypeLabel = {
    [ServerType.ALL]: '全部',
    [ServerType.STANDARD]: '标准MCP',
    [ServerType.REMOTE]: 'Remote',
    [ServerType.LOCAL]: 'Local',
};

export const serverTypeFilterOptions = [
    {
        label: ServerTypeLabel[ServerType.ALL],
        value: ServerType.ALL,
    },
    {
        label: ServerTypeLabel[ServerType.STANDARD],
        value: ServerType.STANDARD,
    },
    {
        label: ServerTypeLabel[ServerType.REMOTE],
        value: ServerType.REMOTE,
    },
    {
        label: ServerTypeLabel[ServerType.LOCAL],
        value: ServerType.LOCAL,
    },
];


export const enum Order {
    /**
     * 综合排序
     */
    DEFAULT = 'default',
    /**
     * 最受欢迎
     */
    POPULAR = 'popular',
    /**
     * 最新发布
     */
    NEWEST = 'publishOrder',
    /**
     * 浏览量 从高到低
     */
    VIEW = 'viewOrder',
    /**
     * 调用量 从高到低
     */
    USE = 'useOrder',
    /**
     * 评论数 从高到低
     */
    COMMENT = 'commentOrder',
}

const OrderLabel = {
    [Order.DEFAULT]: '综合排序',
    [Order.POPULAR]: '最受欢迎',
    [Order.NEWEST]: '最新发布',
    [Order.VIEW]: '浏览量 从高到低',
    [Order.USE]: '调用量 从高到低',
    [Order.COMMENT]: '评论数 从高到低',
};

export const compositeFilterOptions = [
    {
        label: OrderLabel[Order.DEFAULT],
        value: Order.DEFAULT,
    },
    // {
    //     label: OrderLabel[Order.POPULAR],
    //     value: Order.POPULAR,
    // },
    {
        label: OrderLabel[Order.NEWEST],
        value: Order.NEWEST,
    },
    {
        label: OrderLabel[Order.VIEW],
        value: Order.VIEW,
    },
    {
        label: OrderLabel[Order.USE],
        value: Order.USE,
    },
    // {
    //     label: OrderLabel[Order.COMMENT],
    //     value: Order.COMMENT,
    // },
];

export const serverTabItems = [
    // {
    //     label: '精选',
    //     key: 'id1',
    // },
    {
        label: '全部',
        key: 'all',
    },
    {
        label: '我收藏的',
        key: 'favorite',
    },
    {
        label: '我发布的',
        key: 'isMine',
    },
];
