/* eslint-disable max-len */
import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useUserBaseInfo} from '@/design/MCP/useUserBaseInfo';

const Root = styled.div`
    display: flex;
    align-items: center;
    color: #333;
    font-size: 14px;
    padding: 5px 8px;
`;

const Name = styled.div`
    display: flex;
    align-items: center;
`;

const ChineseName = styled.div`
    margin-right: 10px;
`;

const DepartmentName = styled.div`
    color: #999;
`;

const Ruliu = styled.div`
    width: 30px;
    margin-left: 8px;
    margin-top: 8px;
    color: #317ff5;

    &:hover svg {
        border-radius: 5px;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    }
`;
const AvatarWrapper = styled.div`
    width: 38px;
    display: flex;
    align-items: center;
    margin-right: 6px;
    border-radius: 4px;
`;

const Avatar = styled.img`
    width: 38px;
    border-radius: 4px;
`;

export const KStyledUser = ({value}: {value: string}) => {
    const userinfo = useUserBaseInfo(value);

    if (!userinfo) {
        return null;
    }

    return (
        <>
            <Root>
                <AvatarWrapper>
                    <Avatar src={`https://eefe.baidu-int.com/avatars/${userinfo.userName}`} />
                </AvatarWrapper>
                <Flex justify="space-between" flex="1">
                    <div style={{flex: 'none'}}>
                        <Name>
                            <ChineseName>{userinfo.chineseName}</ChineseName>
                            <div>{userinfo.userName}</div>
                        </Name>
                        <DepartmentName>{userinfo.departmentName}</DepartmentName>
                    </div>
                    <Ruliu>
                        <a href={`baidu://message/?id=${userinfo.baiduHi}`}>
                            <svg viewBox="0 0 1024 1024" fill="#317ff5"><path d="M471.8 555.6c-202.7 49.3-305.3-3.9-308.1-5.1-17.5-7.6-25.5-5.1-32.9 9.8-1.4 2.8-10.5 27.6-17.1 44.1-.8 2-2 5.2-2 8.9v.4c.1 5.7 3 12.6 14.5 17.9 21.1 9.5 71.8 27.8 152.6 30.6 18.3 2.8 21.8 15.8 16.6 31.8l-28.6 93c-8 28.4 9.6 42.5 27.6 42.7h.5c7.3 0 14.5-2.4 20.2-6.9 4.4-3.5 193.5-160.3 193.5-160.3 16-14.5 44.6-37.2 74.8-41.8 129.2-15.9 202.8 8.1 216.1 13 8.5 3.1 18 6 27.3-9.7 4-6.7 18.7-47 19-56.9.4-11.1-10.3-14.9-13.9-16-.1 0-.3-.1-.4-.2-15.2-5.3-44.4-13.6-86.8-18.3-18.3-2-40.6-3.5-66.7-3.5-53.4 0-122.7 6.2-206.2 26.5" mask="url(#mask-2_2_)" transform="translate(19 17.757)"></path><path d="M488.6 375.2c-202.7 49.3-305.3-3.9-308.1-5.1-17.5-7.6-25.5-5.1-32.9 9.8-1.4 2.8-10.5 27.6-17.1 44.1-2 5.1-6.8 18.2 12.5 27.2 22.9 10.3 80.7 31 173.9 31 57.5 0 119.5-8 184-23.6 191-46.4 299-11.4 315.4-5.3 8.5 3.2 18 6 27.3-9.7 4-6.7 18.7-47 19-56.9.4-11.1-10.3-14.9-13.9-16-.1-.1-.3-.1-.4-.2-15.2-5.3-44.4-13.6-86.8-18.3-18.3-2-40.6-3.5-66.6-3.5-53.5 0-122.8 6.2-206.3 26.5" mask="url(#mask-4_2_)" transform="translate(19.259 14.879)"></path><path d="M500.6 196.3c-202.7 49.3-305.3-3.9-308.1-5.1-17.5-7.6-25.5-5.1-32.9 9.8-1.4 2.8-10.5 27.6-17.1 44.1-2 5.1-6.8 18.2 12.5 27.2 22.9 10.3 80.7 31 173.9 31 57.6 0 119.5-8 184-23.6 191-46.4 299-11.4 315.4-5.3 8.5 3.1 18 6 27.3-9.7 4-6.7 18.7-47 19-56.9.4-11.1-10.3-14.9-13.9-16-.1-.1-.3-.1-.4-.2-15.2-5.3-44.4-13.6-86.8-18.3-18.3-2-40.6-3.5-66.6-3.5-53.5 0-122.8 6.2-206.3 26.5" mask="url(#mask-6_2_)" transform="translate(19.453 12)"></path></svg>
                        </a>
                    </Ruliu>
                </Flex>
            </Root>
        </>
    );
};
