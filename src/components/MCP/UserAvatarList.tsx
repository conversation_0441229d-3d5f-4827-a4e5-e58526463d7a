/* eslint-disable max-lines */
import {Flex, Popover, Typography} from 'antd';
import {css} from '@emotion/css';
import styled from '@emotion/styled';
import {ReactNode, useMemo} from 'react';
import UserAvatar from '@/design/UserAvatar';
import IconInfoflow from '@/icons/mcp/IconInfoflow';
import IconOutlook from '@/icons/mcp/IconOutlook';
import {usePrefetchUserInfos} from '@/design/MCP/useUserBaseInfo';
import {KStyledUser} from './KStyledUser';


const countCss = css`
  margin-left: 4px;
  padding: 0px 4px;
  cursor: pointer;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  color: #181818;
`;

export const UserAvatarList = ({users = [], max = Infinity}: {users: string[], max: number}) => {
    const visibleUsers = max < users.length ? users.slice(0, max) : users;
    const remainingCount = users.length - max;
    const remainingUsers = useMemo(
        () => {
            return remainingCount > 0 ? users.slice(max) : [];
        },
        [max, remainingCount, users]
    );
    usePrefetchUserInfos(remainingUsers);
    return (
        <Flex gap={8} wrap="nowrap" align="center">
            {visibleUsers.map((user, index) => (
                <UserAvatar key={index} username={user} showText />
            ))}
            {remainingCount > 0 && (
                <Popover
                    content={
                        <Flex vertical gap={12} wrap="wrap">
                            {remainingUsers.map((user, index) => (
                                <KStyledUser key={index} value={user} />
                            ))}
                        </Flex>
                    }
                    placement="bottom"
                >
                    <span className={countCss}>
                        +{remainingCount}
                    </span>
                </Popover>
            )}
        </Flex>
    );
};


const Description = styled.span`
    color: #8f8f8f;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    white-space: pre-line;
    word-break: break-all;
    -webkit-line-clamp: 1;
    flex-shrink: 0;
`;


const renderPositionRoles = (role?: string | string[] | null) => {
    if (Array.isArray(role)) {
        return role.join(',');
    }

    return role ?? '--';
};

interface UserProps {
    userName: string;
    chineseName: string;
    departmentName?: string;
    positionRoles?: string | string[] | null;
}

function User({
    userName,
    chineseName,
    departmentName,
    positionRoles,
    ...props
}: UserProps) {
    const role = renderPositionRoles(positionRoles);
    return (
        <Flex {...props}>
            <UserAvatar username={userName} iconSize="large" />
            <Flex vertical style={{flex: 1, overflow: 'hidden', marginLeft: '12px'}}>
                <Flex>
                    <Typography.Text
                        ellipsis={{tooltip: `${chineseName}${userName}`}}
                        style={{fontSize: '14px', maxWidth: '155px'}}
                    >
                        {chineseName}
                        <span style={{color: '#8f8f8f', marginLeft: '5px'}}>{userName}</span>
                    </Typography.Text>
                </Flex>
                <Flex style={{fontSize: '12px', color: '#5C5C5C'}}>
                    {role && <Description>{role}</Description>}
                    <Description style={{marginLeft: '5px'}}>{departmentName || '无部门信息'}</Description>
                </Flex>
            </Flex>
        </Flex>
    );
}

export interface IUser{
    /** 百度账号 */
    baiduHi?: string;
    /** 邮箱 */
    email?: string;
    /** 用户名 */
    userName: string;
    /** 中文名 */
    chineseName: string;
    /** 部门名称 */
    departmentName: string;
    /** 职位角色信息 */
    positionRoles: string[] | null;
}

const CardRoot = styled(Flex)`
    width: 300px;
    padding: 12px 12px;
    border-radius: 4px;
`;

const UserContactContainer = styled(Flex)`
    > a {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 24px;
        width: 32px;
        background-color: #f7f7f7;
        color: #2e2e2e;

        &:hover {
            background-color: #e8e8e8;
        }
    }

    --radius: 12px;
    > a {
        border-radius: var(--radius);
    }

    > a:first-of-type:not(:last-child) {
        position: relative;
        border-radius: var(--radius) 0 0 var(--radius);

        &:after {
            content: '';
            position: absolute;
            right: 0;
            width: 1px;
            height: 12px;
            background: #d9d9d9;
        }

        + a {
            border-radius: 0 var(--radius) var(--radius) 0;
        }
    }

    > svg{
        font-size: 18px;
    }
`;


export function UserCard({user}: {user: IUser}) {
    return (
        <CardRoot justify="space-between" align="center">
            <User
                key={user.userName}
                userName={user.userName}
                chineseName={user.chineseName}
                departmentName={user.departmentName}
                positionRoles={user.positionRoles}
            />
            <UserContactContainer>
                {user.email && (
                    <a href={`mailto:${user.email}`}>
                        <IconOutlook />
                    </a>
                )}
                {user.baiduHi && (
                    <a href={`baidu://message/?id=${user.baiduHi}`}>
                        <IconInfoflow />
                    </a>
                )}
            </UserContactContainer>
        </CardRoot>
    );
}

interface UserCardListProps {
    users: IUser[];
    children: ReactNode;
}

export const UserCardList = ({users = [], children}: UserCardListProps) => {
    const content = useMemo(
        () => {
            return (
                <Flex gap={8} vertical style={{maxWidth: '300px'}}>
                    {users.map(user => (
                        <UserCard key={user.userName} user={user} />
                    ))}
                </Flex>
            );
        },
        [users]
    );
    return (
        <Popover
            content={content}
            placement="bottom"
            trigger="click"
        >
            {children}
        </Popover>
    );
};
