import {StarFilled, StarOutlined} from '@ant-design/icons';
import {Button, ButtonProps, message} from '@panda-design/components';
import {MouseEvent, CSSProperties, useRef} from 'react';
import {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';

interface Props {
    serverId: number;
    favorite: boolean;
    refresh: () => void;
    size?: ButtonProps['size'];
    style?: CSSProperties;
    showText?: boolean;
    onFavoriteChange?: (serverId: number, favorite: boolean) => void;
}

export const MCPCollectButton = ({
    serverId,
    refresh,
    favorite,
    size,
    style,
    showText = true,
    onFavoriteChange,
}: Props) => {
    const buttonRef = useRef<HTMLElement>(null);

    const handleClick = async (e: MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        const scrollElement = document.getElementById('scrollableDiv')
                             || document.querySelector('.ant-layout-content')
                             || document.documentElement;
        const initialScrollTop = scrollElement?.scrollTop || 0;

        try {
            if (favorite) {
                await apiDeleteServerFavorite({mcpServerId: serverId});
            } else {
                await apiPutServerFavorite({mcpServerId: serverId});
            }
            message.success(favorite ? '取消收藏成功' : '收藏成功');

            if (onFavoriteChange) {
                onFavoriteChange(serverId, !favorite);
            } else {
                refresh();

                if (scrollElement) {
                    setTimeout(() => {
                        scrollElement.scrollTo({
                            top: initialScrollTop,
                            behavior: 'auto',
                        });
                    }, 100);
                }
            }
        } catch (error) {
            message.error('操作失败，请重试');
        }
    };
    return (
        <Button
            ref={buttonRef}
            onClick={handleClick}
            type="text"
            size={size}
            style={style}
            icon={
                favorite
                    ? <StarFilled style={{color: '#FFA400'}} />
                    : <StarOutlined />
            }
        >
            {showText ? '收藏' : undefined}
        </Button>
    );
};
