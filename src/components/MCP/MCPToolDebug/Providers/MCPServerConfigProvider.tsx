import {Form} from 'antd';
import constate from 'constate';
import {useCallback, useState} from 'react';
import {message} from '@panda-design/components';
import {isNil} from 'lodash';
import {apiGetMCPGlobalVars, apiGetMCPServer, apiPostMCPGlobalVars} from '@/api/mcp';
import {MCPServerBase, MCPServerParam} from '@/types/mcp/mcp';

// interface MCPBaseInfo {
//   name: string;
//   // 鉴权方法
//   authDescription?: string;
//   user: string;
// }

export interface GlobalVar {
    name: string;
    dataType: string;
    description: string | null;
    value: string | null;
    required: boolean;
    defaultValue?: string | null;
}

const validate = (globalVars: GlobalVar[]) => {
    const inValid = globalVars?.some(item => {
        return item.required && isNil(item.value);
    });
    return !inValid;
};

export function combineResponse(serverParams: MCPServerParam[], globalVars: GlobalVar[]) {
    return serverParams?.map(param => {
        const cur = globalVars?.find(item => item.name === param.name);
        return {
            ...param,
            value: cur?.value || param.value,
        };
    });
}

/**
 * temporary: 在工具调试时，如果不是在playground的场景下，全局变量不会在后端储存，用户即填即用。离开页面也清除
 */
export const [MCPGlobalVarsProvider, useMCPGlobalVarsContext] = constate(
    (props: { mcpId: number, temporary: boolean }) => {
        const {mcpId, temporary} = props;
        // 这个状态用于渲染对应的表单，和外部取上一次保存的值
        const [globalVars, setGlobalVars] = useState<GlobalVar[]>([]);
        // 储存表单信息，因为在playground中，外部无法传入完整的表单信息供《服务配置》组件使用，这样可以避免对外部业务有过多干扰。多调一次接口是可以接受的。
        const [mcpServerInfo, setMcpServerInfo] = useState<MCPServerBase | null>(null);
        const [form] = Form.useForm();
        const [validateStatus, setValidateStatus] = useState(false);
        const validateGlobalVars = useCallback(
            () => form.validateFields().then(() => true).catch(() => false).then(status => {
                setValidateStatus(status);
                return status;
            }),
            [form]
        );
        const initFormValue = useCallback(
            (globalVars: GlobalVar[]) => {
            // 将globalVars以name为key，以value为值，转换为key-value的形式
                const values = globalVars?.reduce((acc, curr) => {
                    acc[curr.name] = curr.value;
                    return acc;
                }, {} as {[key: string]: string});
                form.setFieldsValue(values);
            },
            [form]
        );
        const syncGlobalVarsToState = useCallback(
            (newVars: Array<{name: string, value: string}>) => {
            // 将newvars中的value更新到对应的globalVars中
                const result = globalVars.map(item => {
                    const findItem = newVars.find(newItem => newItem.name === item.name);
                    if (findItem) {
                        return {
                            ...item,
                            value: findItem.value,
                        };
                    }
                    return item;
                });
                setGlobalVars(result);
            },
            [globalVars]
        );
        /**
         * 如果保存成功会返回true，否则返回具体的error
         */
        const saveGlobalVars = useCallback(
            async (onSuccess?: (vars: GlobalVar[]) => void): Promise<GlobalVar[]|Error> => {
                try {
                    const formValues = await form.validateFields();
                    const vars = globalVars.map(item => {
                        return {
                            ...item,
                            value: formValues[item.name],
                        };
                    });

                    try {
                        if (!temporary) {
                            await apiPostMCPGlobalVars({serverId: mcpId, params: vars});
                            onSuccess?.(vars);
                        }
                        syncGlobalVarsToState(vars);
                        setValidateStatus(true);
                        return vars;
                    } catch (e) {
                        message.error(e.response?.data?.msg || '服务配置保存失败');
                        return e;
                    }
                } catch (e) {
                    message.error('服务配置表单校验失败');
                    return e;
                }
            },
            [form, globalVars, mcpId, syncGlobalVarsToState, temporary, setValidateStatus]
        );

        // 这个会在serverConfigForm中在初始化时被调用
        const initGlobalVars = useCallback(
            () => {
                // 如果用户从没有填过全局变量，apiGetMCPGlobalVars接口会返回一个空数组，此时需要从apiGetMCPServer接口中获取参数
                if (mcpId) {
                    Promise.all([
                        apiGetMCPServer({mcpServerId: mcpId}),
                        apiGetMCPGlobalVars({mcpServerId: mcpId}),
                    ]).then(([serverRes, globalVarsRes]) => {
                        const res = combineResponse(serverRes.serverParams ?? [], globalVarsRes);
                        setGlobalVars(res);
                        setMcpServerInfo(serverRes);
                        initFormValue(res);
                        setValidateStatus(validate(res));
                    });
                }
            },
            [mcpId, initFormValue, setValidateStatus]
        );

        // 弹窗关闭时，需要把表单的改动重置掉。但是如果是用户点击保存后关闭弹窗，则需要把新的globalVars传入，否则该方法依赖的globalVars仍是上一次保存的值。
        const resetGlobalVars = useCallback(
            (newVars?: GlobalVar[]) => {
                initFormValue(newVars ?? globalVars);
            },
            [initFormValue, globalVars]
        );
        return {
            globalVarsFormInstance: form,
            globalVars,
            validateGlobalVars,
            saveGlobalVars,
            mcpServerInfo,
            inPlayground: !temporary,
            initGlobalVars,
            validateStatus,
            resetGlobalVars,
        };
    });
