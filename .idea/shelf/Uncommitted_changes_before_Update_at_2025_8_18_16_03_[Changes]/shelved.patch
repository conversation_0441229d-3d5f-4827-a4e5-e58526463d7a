Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision e0ebd8b86913da61fddc206f1f515f39de9d809b)
+++ b/.gitignore	(date 1755261160704)
@@ -23,3 +23,32 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/vcs.xml
+.idea/workspace.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_16_03__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_19_27__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/cardBg.png
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_10_42_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_13_49_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_16_03_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_17_45_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_15_19_27_\[Changes]/shelved.patch
